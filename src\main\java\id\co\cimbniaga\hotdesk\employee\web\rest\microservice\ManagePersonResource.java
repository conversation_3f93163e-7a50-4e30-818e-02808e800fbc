package id.co.cimbniaga.hotdesk.employee.web.rest.microservice;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.sql.SQLException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import id.co.cimbniaga.hotdesk.employee.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lowagie.text.DocumentException;

import id.co.cimbniaga.hotdesk.employee.config.Constants;
import id.co.cimbniaga.hotdesk.employee.service.AssignmentService;
import id.co.cimbniaga.hotdesk.employee.service.ManagePersonService;
import id.co.cimbniaga.hotdesk.employee.service.dto.ApproveTaskDTO;
import id.co.cimbniaga.hotdesk.employee.service.restcontrolleradvice.CustomBadRequestException;
import id.co.cimbniaga.hotdesk.employee.util.ManagePersonServiceHelper;
import id.co.cimbniaga.hotdesk.employee.web.rest.feign.response.GeneralBodyResponse;
import id.co.cimbniaga.hotdesk.employee.web.rest.interfaces.ManagePersonValidator;

/**
 * REST controller for managing
 * {@link id.co.cimbniaga.hotdesk.employee.domain.MasterPeople}.
 */
@RestController
@RequestMapping("/api/employees")
public class ManagePersonResource {

    private final Logger log = LoggerFactory.getLogger(ManagePersonResource.class);

    private static final String ENTITY_NAME = "employeeserviceManagePerson";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ManagePersonService managePersonService;

    private final AssignmentService assignmentService;

    private final ManagePersonServiceHelper managePersonServiceHelper;

    private final ManagePersonValidator managePersonValidator;

    public ManagePersonResource(ManagePersonService managePersonService, AssignmentService assignmentService, ManagePersonServiceHelper managePersonServiceHelper, ManagePersonValidator managePersonValidator) {
        this.managePersonService = managePersonService;
        this.assignmentService = assignmentService;
        this.managePersonServiceHelper = managePersonServiceHelper;
        this.managePersonValidator = managePersonValidator;
    }

    /**
     *
     * @param managePersonDTO
     * @param pageable
     * @param authentication
     * @return
     */
    @PostMapping("/managePerson")
    public ResponseEntity<?> managePerson(@RequestBody ManagePersonDTO managePersonDTO, Pageable pageable, Authentication authentication) {
        log.debug("REST request to get manage person");
        managePersonValidator.isCorrectOwnership(authentication, managePersonDTO.getNip());

        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonValidator.isTabExists(managePersonDTO.getTab());

        return managePersonServiceHelper.managePersonHelper(managePersonDTO, authentication, pageable, "FO");

    }

    @PostMapping("/managePerson/detail")
    public ResponseEntity<?> managePersonDetail(@RequestBody ManagePersonDTO managePersonDTO, Authentication authentication) {
        log.debug("REST request to get manage person");
        managePersonValidator.isCorrectOwnership(authentication, managePersonDTO.getNip());
        ResponseEntity<?> result = null;
        String[] tabValue = { "personalIdentificationNumber", "educationHistory", "previousEmploymentHistory", "familyAndEmergencyContact",
                "bankAccount", "photoAndDigitalIdCard", "vaccinationCovid19" };

        if (!Arrays.asList(tabValue).contains(managePersonDTO.getTab())) {
            throw new CustomBadRequestException(400, "Bad Request", "wrong tab value");
        }

        result = ResponseEntity.ok().body(new GeneralBodyResponse(200, "Ok", "Successfully get detail personal info",
                managePersonService.detailPersonalInfoBO(managePersonDTO.getId(), managePersonDTO.getTab(), managePersonDTO.getEffectiveStartDate(),managePersonDTO.getFilterDate(), managePersonDTO.getNip(), "FO")));

        return result;
    }

    @PostMapping("/managePerson/downloadEmployeeSummary")
    public void downloadEmployeeSummary(@RequestBody SelectSummaryEmployeeDTO selectSummaryEmployeeDTO,
            HttpServletResponse response, Authentication authentication) throws DocumentException, IOException,
            NoSuchMethodException, IllegalAccessException, InvocationTargetException, SQLException {
        managePersonValidator.isAuthenticationEmpty(authentication);
        log.info("ini nip " + selectSummaryEmployeeDTO.getNip());
        log.info("ini auth " + authentication);
        managePersonValidator.isCorrectOwnership(authentication, selectSummaryEmployeeDTO.getNip());
        log.debug("REST request to download employee profile new");
        managePersonServiceHelper.downloadEmployeeSummaryHelper(selectSummaryEmployeeDTO, response, authentication);

    }

    @GetMapping("/managePersonAuth")
    public ResponseEntity<?> managePersonAuth(@RequestParam(value = "nip", required = true) String nip,
                                              @RequestParam(value = "tab") String tab, @RequestParam(value = "assignmentNumber", required = false) String assignmentNumber,
                                              @RequestParam(value = "effectiveStartDate", required = false) LocalDate effectiveStartDate,
                                              @RequestParam(value = "filterDate", required = false) LocalDate filterDate,
                                              @RequestParam(value = "assignmentId", required = false) String assignmentId, Pageable pageable,
                                              Authentication authentication) {
        log.debug("REST request to get manage person");
        ResponseEntity<?> result = null;
        String[] tabValue = { "assignment", "personalInfo" };

        managePersonValidator.isCorrectOwnership(authentication, nip);

        if (!Arrays.asList(tabValue).contains(tab)) {
            throw new CustomBadRequestException(400, "Bad Request", "wrong tab value");
        }

        if (tab.equalsIgnoreCase("assignment")) {
            // result = ResponseEntity.ok().body(new GeneralBodyResponse(200, "Ok",
            // "Successfully get assignment info", employeeService.assignmentInfoBO(nip,
            // pageable)));
//            DetailAssignmentResponseDTO detailAssignment = assignmentService.detailAssignment(nip, effectiveStartDate, filterDate);
//            result = ResponseEntity.ok().body(new GeneralBodyResponse(200, "Ok", "Successfully get assignment info", detailAssignment));
            result = ResponseEntity.ok().body(new GeneralBodyResponse(200, "Ok", "Successfully get assignment info", managePersonService.assignmentDetail(assignmentNumber, nip, filterDate, assignmentId)));

        } else if (tab.equalsIgnoreCase("personalInfo")) {
            result = ResponseEntity.ok().body(new GeneralBodyResponse(200, "Ok", "Successfully get personal info",
                managePersonService.personalInfoBO(nip, effectiveStartDate, filterDate, pageable, "FO")));
        }
        return result;
    }

    /**
     *
     * @param managePersonDTO
     * @param authentication
     * @return
     */
    @PostMapping("/managePerson/detailEmployee")
    public ResponseEntity<?> detailEmployee(@RequestBody ManagePersonDTO managePersonDTO, Authentication authentication) {
        managePersonValidator.isCorrectOwnership(authentication,managePersonDTO.getNip());
        if(managePersonDTO.getNip().equalsIgnoreCase(authentication.getName())){
            try {
                log.debug("REST request to get detailEmployee ");
                EmployeeAssignmentCardResponseDTO detailEmployee = managePersonService.detailEmployee(managePersonDTO.getNip());
                return ResponseEntity.ok().body(new GeneralBodyResponse(200, "Ok", "Successfully detailEmployee", detailEmployee));
            } catch (Exception e) {
                log.error("an error occured MasterPeopleResource! :" + e.getMessage());
                throw new CustomBadRequestException(520, "Server Error", "an error occured on server");
            }
        } else {
            throw new CustomBadRequestException(
                HttpStatus.FORBIDDEN.value(),
                HttpStatus.FORBIDDEN.getReasonPhrase(),
                Constants.SharedErrorResponse.NOT_ALLOWED);
        }

    }

    @PostMapping("/managePerson/listAssignment")
    public ResponseEntity<?> listAssignment(@RequestBody ManagePersonDTO managePersonDTO, Authentication authentication) {
        managePersonValidator.isCorrectOwnership(authentication,managePersonDTO.getNip());
        if (authentication == null) {
            throw new CustomBadRequestException(
                HttpStatus.UNAUTHORIZED.value(),
                HttpStatus.UNAUTHORIZED.getReasonPhrase(),
                Constants.errorResponseDetail.NO_AUTH_FOUND);
        }
        if(managePersonDTO.getNip().equalsIgnoreCase(authentication.getName())) {
            try {
                log.debug("REST request to get list assignment : {}");
                Optional<List<ListAssignmentResponseDTO>> listAssignment = managePersonService.getListAssignment(managePersonDTO.getNip());
                return ResponseEntity.ok().body(new GeneralBodyResponse(200, "Ok", "Successfully get list assignment", listAssignment));
            } catch (Exception e) {
                log.error("an error occured MasterPeopleResource! :" + e.getMessage());
                throw new CustomBadRequestException(520, "Server Error", "an error occured on server");
            }
        } else {
            throw new CustomBadRequestException(
                HttpStatus.FORBIDDEN.value(),
                HttpStatus.FORBIDDEN.getReasonPhrase(),
                Constants.SharedErrorResponse.NOT_ALLOWED);
        }
    }

    /**
     *
     * @param updateSocialMediaDTO
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping("/managePerson/socmedUpdate")
    public ResponseEntity<?> updateSocialMedia( @RequestBody UpdateSocialMediaDTO updateSocialMediaDTO,
                                                Authentication authentication) throws JsonProcessingException {
        log.debug("REST request to update/create Other Info/Social Media");

        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonServiceHelper.socialMediaCreateUpdateHelper(updateSocialMediaDTO, authentication);

        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok",
                "Successfully update/create Other Info/Social Media", null));
    }

    /**
     *
     * @param updatePreviousEmploymentDto
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping("/managePerson/previousEmploymentUpdate")
    public ResponseEntity<?> updatePreviousEmployment(@RequestPart("managePersonRequest") UpdatePreviousEmploymentDto managePersonRequest,
                                                      Authentication authentication,
//                                                      @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments,
                                                      HttpServletRequest httpServletRequest) throws JsonProcessingException {
        log.debug("REST request to update/create Previous Employment History");
        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonServiceHelper.previousEmployerCreateUpdateHelper(managePersonRequest, authentication, httpServletRequest);
        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok",
                "Successfully update/create Previous Employment History", null));
    }

    /**
     *
     * @param updateContactEmployeeDTO
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping("/managePerson/employeeContactUpdate")
    public ResponseEntity<?> updateContactEmployee(@RequestBody UpdateContactEmployeeDTO updateContactEmployeeDTO,
                                                   Authentication authentication) throws JsonProcessingException {
        log.debug("REST request to update/create Contact Employee");
        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonServiceHelper.employeeContactCreateUpdateHelper( updateContactEmployeeDTO, authentication);
        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok", "Successfully update/create Contact Employee",
                null));
    }

    /**
     *
     * @param updateEducationHistoryDto
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping("/managePerson/educationUpdate")
    public ResponseEntity<?> updateEducation(@RequestPart("managePersonRequest") UpdateEducationHistoryDto managePersonRequest,
                                             Authentication authentication,
//                                             @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments,
                                             HttpServletRequest httpServletRequest) throws JsonProcessingException {
        log.debug("REST request to update/create Education History");
        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonServiceHelper.educationCreateUpdateHelper(managePersonRequest, authentication, httpServletRequest);
        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok", "Successfully update/create Education History",
                null));
    }

    /**
     *
     * @param updateAddressRequestDto
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
//    @PostMapping("/managePerson/addressUpdate")
//    public ResponseEntity<?> createUpdateAddress(@RequestPart("managePersonRequest") UpdateAddressRequestDto updateAddressRequestDto,
//                                                 Authentication authentication,
//                                                 @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments) throws JsonProcessingException {
//        log.debug("REST request to update/create Address");
//        managePersonValidator.isAuthenticationEmpty(authentication);
//        managePersonServiceHelper.addressCreateUpdateHelper(updateAddressRequestDto, authentication, attachments);
//        return ResponseEntity.ok()
//            .body(new GeneralBodyResponse(200, "Ok", "Successfully update/create Address", null));
//    }

    @PostMapping("/managePerson/addressUpdate")
    public ResponseEntity<?> createUpdateAddress(@RequestBody UpdateAddressRequestDto updateAddressRequestDto,
                                                 Authentication authentication) throws JsonProcessingException {
        log.debug("REST request to update/create Address");
        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonServiceHelper.addressCreateUpdateHelper(updateAddressRequestDto, authentication);
        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok", "Successfully update/create Address", null));
    }

    /**
     *
     * @param updateNationalIdentifierDTO
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping("/managePerson/nationalIdUpdate")
    public ResponseEntity<?> updateNationalIdentifiers(@RequestPart("managePersonRequest") UpdateNationalIdentifierDTO updateNationalIdentifierDTO,
                                                       Authentication authentication,
//                                                       @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments,
                                                       HttpServletRequest httpServletRequest) throws JsonProcessingException {
        log.debug("REST request to update/create National Identifiers");
        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonServiceHelper.nationalIdentifiersCreateUpdateHelper(updateNationalIdentifierDTO, authentication, httpServletRequest);
        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok",
                "Successfully update/create National Identifiers", null));
    }

    /**
     *
     * @param updatePersonInfoRequestDTO
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping("/managePerson/personInfoUpdate")
    public ResponseEntity<?> updatePerson(@RequestPart("managePersonRequest") UpdatePersonInfoRequestDTO updatePersonInfoRequestDTO,
                                          Authentication authentication,
                                          @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments) throws JsonProcessingException {
        log.debug("REST request to update/create Person Info");
        managePersonValidator.isAuthenticationEmpty(authentication);
        return managePersonServiceHelper.personInfoCreateUpdateHelper(updatePersonInfoRequestDTO, authentication, attachments);
    }


    @PostMapping("/managePerson/photoUpdate")
    public ResponseEntity<?> updatePhotoAndIdCard(
        @RequestPart("managePersonRequest") UpdatePhotoRequestDTO managePersonRequest,
        @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments,
        Authentication authentication) throws IOException {
        log.debug("REST request to update/create Photo And ID Card : {}");
        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonServiceHelper.photoCreateUpdateHelper(managePersonRequest, authentication, attachments);
        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok",
                "Successfully update/create Photo And ID Card", null));
    }

    /**
     *
     * @param valueSearch
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
    @GetMapping("/managePerson/searchEmployee")
    public ResponseEntity<?> searchEmployee(@RequestParam(value = "valueSearch", required = false) String valueSearch,
            Authentication authentication) throws JsonProcessingException {
            log.debug("REST request to search employee");
            managePersonValidator.isAuthenticationEmpty(authentication);

            return ResponseEntity.ok()
                            .body(new GeneralBodyResponse(200, "Ok", "Successfully Search Employee", managePersonService.searchEmployee(valueSearch)));
    }

    @GetMapping("/managePerson/searchEmployee/detail")
    public ResponseEntity<?> searchEmployeeDetail(@RequestParam(value = "nip", required = true) String nip,
            Authentication authentication) throws JsonProcessingException {
            log.debug("REST request to search employee : {}");
            managePersonValidator.isAuthenticationEmpty(authentication);

            return ResponseEntity.ok()
                            .body(new GeneralBodyResponse(200, "Ok", "Successfully Search Employee", managePersonService.detailSearchEmployee(nip)));
    }

    /**
     *
     * @param request
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
//    @PostMapping("/managePerson/familyUpdate")
//    public ResponseEntity<?> createUpdateFamily(@RequestBody UpdateFamilyRequestDTO request, Authentication authentication) throws JsonProcessingException {
//        log.debug("REST request to update/create family");
//        managePersonValidator.isAuthenticationEmpty(authentication);
//        managePersonServiceHelper.familyCreateUpdateHelper(request, authentication);
//        return ResponseEntity.ok()
//            .body(new GeneralBodyResponse(200, "Ok", "Successfully update/create family", null));
//    }

    @PostMapping("/managePerson/familyUpdate")
    public ResponseEntity<?> createUpdateFamily(@RequestPart("managePersonRequest") UpdateFamilyRequestBODTO managePersonRequest,
                                                HttpServletRequest request, Authentication authentication) throws JsonProcessingException {
        log.debug("REST request to update/create family");
        managePersonValidator.isAuthenticationEmpty(authentication);
        managePersonServiceHelper.familyCreateUpdateHelper(managePersonRequest, authentication, request);
        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok", "Successfully update/create family", null));
    }

    /**
     *
     * @param approveTask
     * @param authentication
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping("/managePerson/approval")
    public ResponseEntity<?> approval(@RequestBody ApproveTaskDTO approveTask, Authentication authentication)
        throws JsonProcessingException {
        log.debug("REST request to Approve/Reject Person Info");
        final UsernamePasswordAuthenticationToken up = (UsernamePasswordAuthenticationToken) authentication;

        managePersonServiceHelper.approvalHelper(approveTask, authentication);
        return ResponseEntity.ok().body(
            new GeneralBodyResponse(200, "Ok", "Successfully Approve/Reject Person Info", null));
    }


    @GetMapping("/managePerson/years-of-service")
    public ResponseEntity<?> getYearOfService( Authentication authentication) throws JsonProcessingException {
        log.debug("REST request to get years of service");
        managePersonValidator.isAuthenticationEmpty(authentication);
        final UsernamePasswordAuthenticationToken up = (UsernamePasswordAuthenticationToken) authentication;

        Map<String, Object> response = managePersonService.getYearOfService(up.getName(), authentication);

        return ResponseEntity.ok().body(new GeneralBodyResponse(200, "Ok", "Successfully get years of service", response));
    }

    @GetMapping("/assignments/organization-chart")
    public ResponseEntity<?> orgChart(@RequestParam(value = "employeeNumber", required = false) String employeeNumber, @RequestParam(value = "fsPositionId", required = false) Long fsPositionId, Authentication authenticatione) throws ParseException, IllegalAccessException, IOException {
        log.debug("REST request to get organization-chart : {}");
        return assignmentService.organizationChart(employeeNumber, fsPositionId);
    }

    @PostMapping("/managePerson/updateMaritalStatus")
    public ResponseEntity<?> updateMaritalStatus(
        @ModelAttribute UpdateMaritalStatusRequestDTO requestDTO,
        @RequestParam(value = "attachments", required = false) List<MultipartFile> attachments,
        Authentication authentication) throws JsonProcessingException {

        log.debug("REST request to update marital status: {}", requestDTO);
        managePersonValidator.isAuthenticationEmpty(authentication);

        // Get user from authentication
        final UsernamePasswordAuthenticationToken up =
            (UsernamePasswordAuthenticationToken) authentication;
        String personNumber = up.getName();

        managePersonService.requestMaritalStatusUpdate(requestDTO, personNumber,
            attachments, "FO");

        return ResponseEntity.ok()
            .body(new GeneralBodyResponse(200, "Ok",
                "Marital status update request submitted for approval", null));
    }

}
