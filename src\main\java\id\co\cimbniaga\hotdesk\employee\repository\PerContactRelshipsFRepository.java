package id.co.cimbniaga.hotdesk.employee.repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import id.co.cimbniaga.hotdesk.employee.domain.PerContactRelshipsF;
import id.co.cimbniaga.hotdesk.employee.dto.FamilyDTO;
import id.co.cimbniaga.hotdesk.employee.service.dto.response.IEmployeeChildsIdAndName;
import id.co.cimbniaga.hotdesk.employee.web.rest.feign.response.EmployeeBenefitContactRelships;
import id.co.cimbniaga.hotdesk.employee.web.rest.feign.response.EmployeeStaffloanContactRelships;

/**
 * Spring Data SQL repository for the PerContactRelshipsF entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PerContactRelshipsFRepository extends JpaRepository<PerContactRelshipsF, Long> {

	List<PerContactRelshipsF> findByContactRelationshipIdAndEffectiveEndDateIsNotNullOrderByEffectiveStartDateAsc(long contactRelationshipId);
        // aman
        @Query(value = "select o.* from per_contact_relships_f o left join per_all_people_f  p on p.person_id = o.contact_person_id  and p.effective_start_date <= now() and p.effective_end_date >= now() where p.person_number = :personNumber and o.contact_type = :contactType", nativeQuery = true)
        Optional<PerContactRelshipsF> findSpouseByEmployeeNumber(
                        @Param("personNumber") String personNumber,
                        @Param("contactType") String contactType);

        @Query(value = "select o.* from per_contact_relships_f o where o.person_id  = :personId and :filterDate between effective_start_date and effective_end_date and o.contact_type = :contactType limit 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findContactByPersonIdAndDateAndContactType(
                        @Param("personId") Long personId,
                        @Param("filterDate") LocalDate filterDate,
                        @Param("contactType") String contactType);

        @Query(value = "select o.* from per_contact_relships_f o where o.person_id = :personId and :filterDate between effective_start_date and effective_end_date and o.contact_type in :contactTypes limit 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findContactByPersonIdAndDateAndContactTypes(
                        @Param("personId") Long personId,
                        @Param("filterDate") LocalDate filterDate,
                        @Param("contactTypes") List<String> contactTypes);

        @Query(value = "select o.* from per_contact_relships_f o where o.person_id = :personId and :filterDate between effective_start_date and effective_end_date and (o.contact_type in :exactTypes or o.contact_type like :pattern) limit 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findContactByPersonIdAndDateAndContactPattern(
                        @Param("personId") Long personId,
                        @Param("filterDate") LocalDate filterDate,
                        @Param("exactTypes") List<String> exactTypes,
                        @Param("pattern") String pattern);

        @Query(value = "select o.* from per_contact_relships_f o "
        		+ "where o.person_id  = :personId and o.contact_type = :contactType "
        		+ "ORDER BY "
        		+ "  CASE WHEN :filterDate BETWEEN effective_start_date AND effective_end_date THEN 0 ELSE 1 END,  "
        		+ "  ABS(DATEDIFF(effective_start_date, :filterDate)) "
        		+ "LIMIT 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findContactByPersonIdAndContactTypeClosestDate(
                        @Param("personId") Long personId,
                        @Param("filterDate") LocalDate filterDate,
                        @Param("contactType") String contactType);

        @Query(value = "SELECT * from per_contact_relships_f o where o.person_id  = :personId and o.contact_type = :contactType and o.active =false "
        		+ "order by effective_end_date desc limit 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findContactByPersonIdAndDateAndContactTypeInApproval(
                        @Param("personId") Long personId,
                        @Param("contactType") String contactType);

        @Query(value = "select o.* from per_contact_relships_f o where o.contact_person_id  = :contact_person_id and :filterDate between effective_start_date and effective_end_date and o.contact_type = :contactType limit 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findByPersonIdAndDateAndContactType(
                        @Param("contact_person_id") Long contact_person_id,
                        @Param("filterDate") LocalDate filterDate,
                        @Param("contactType") String contactType);

        @Query(value = "select * from per_contact_relships_f pcrf where pcrf.contact_relationship_id =:fsContactId and :filterDate between effective_start_date and effective_end_date order by effective_end_date limit 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findByFsContactId(
                        @Param("fsContactId") Long fsContactId,
                        @Param("filterDate") LocalDate filterDate);

        @Query(value = "select * from per_contact_relships_f pcrf where pcrf.contact_relationship_id =:fsContactId "
        		+ "ORDER BY "
        		+ "  CASE WHEN :filterDate BETWEEN effective_start_date AND effective_end_date THEN 0 ELSE 1 END, "
        		+ "  ABS(DATEDIFF(effective_start_date, :filterDate)) "
        		+ "LIMIT 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findByFsContactIdAndClosestDate(
                        @Param("fsContactId") Long fsContactId,
                        @Param("filterDate") LocalDate filterDate);

        @Query(value = "select * from per_contact_relships_f pcr where pcr.contact_relationship_id = :contactRelationshipId and effective_start_date = :effectiveStartDate limit 1", nativeQuery = true)
        Optional<PerContactRelshipsF> findByContactRelationshipId(@Param("contactRelationshipId") Long contactType,
                        @Param("effectiveStartDate") String effectiveStartDate);

        // aman
        @Query(value = "select o.contact_type, o.primary_contact_flag, o.emergency_contact_flag " +
                        ", o.marriage_date, IF(SUBSTRING(e.person_number,1,1)=8,'',e.person_number) AS rel_nik, n.last_name AS rel_name, e.date_of_birth, s.svalue as relationship, p.person_number as contact_number,n.person_id, "
                        + "MAX(CASE WHEN ni.national_identifier_type = 'ORA_HRX_KTP' THEN ni.national_identifier_number END) AS national_identifier "+
                        "from per_contact_relships_f o " +
                        "left join per_all_people_f  p on p.person_id = o.person_id  and p.effective_start_date <= now() and p.effective_end_date >= now() "
                        +
                        "left join per_all_people_f e on e.person_id = o.contact_person_id  and e.effective_start_date <= now() and e.effective_end_date >= now() "
                        +
                        "LEFT JOIN per_person_names_f n ON n.person_id=e.person_id  and (CURDATE() >= n.effective_start_date and CURDATE() <= n.effective_end_date) and n.active = true "
                        +
                        "left join system_config s on o.contact_type = s.parameter " +
						"left join per_national_identifiers ni ON ni.person_id = e.person_id " +
                        "where p.person_number = :personNumber and o.contact_type = :contactType " +
                        "and (n.effective_start_date < now() and n.effective_end_date > now()) " +
                        "and (e.effective_start_date < now() and e.effective_end_date > now()) " +
                        "and (p.effective_start_date < now() and p.effective_end_date > now()) " +
                        "and (o.effective_start_date < now() and o.effective_end_date > now()) and s.vgroup = 'FAMILY_TYPE_CODE' "+
						"group by o.contact_type, o.primary_contact_flag, o.emergency_contact_flag, o.marriage_date," +
						"e.person_number, n.last_name, e.date_of_birth, s.svalue, p.person_number, n.person_id", nativeQuery = true)
        EmployeeStaffloanContactRelships findRelshipByEmployeeNumber(
                        @Param("personNumber") String personNumber,
                        @Param("contactType") String contactType);

        // aman
        @Query(value = "select o.contact_type, o.primary_contact_flag, o.emergency_contact_flag , o.marriage_date, pktp.national_identifier_number as rel_nik,  "
        		+ "n.last_name AS rel_name, e.date_of_birth, s.svalue as relationship, p.person_number as contact_number , n.person_id,  "
				/* + "-- e.person_number as nik_cimb, " */
        		+ "e.town_of_birth, e.gender ,CASE when e.person_type = 'EMPLOYEE' then e.person_number else null END nik_cimb ,e.date_of_death "
        		+ "from per_contact_relships_f o  "
        		+ "left join per_all_people_f  p on p.person_id = o.person_id  and p.effective_start_date <= now() and p.effective_end_date >= now()  "
        		+ "left join per_all_people_f e on e.person_id = o.contact_person_id  and e.effective_start_date <= now() and e.effective_end_date >= now()  "
        		+ "LEFT JOIN per_person_names_f n ON n.person_id=e.person_id  and (CURDATE() >= n.effective_start_date and CURDATE() <= n.effective_end_date)  "
        		+ "left join system_config s on o.contact_type = s.parameter  "
        		+ "left join per_national_identifiers pktp on pktp.person_id = e.person_id and pktp.national_identifier_type = 'ORA_HRX_KTP' "
        		+ "where 0=0 "
        		+ "and p.person_number = :personNumber  "
        		+ "and (n.effective_start_date < now() and n.effective_end_date > now())  "
        		+ "and (e.effective_start_date < now() and e.effective_end_date > now())  "
        		+ "and (p.effective_start_date < now() and p.effective_end_date > now()) "
        		+ "and (o.effective_start_date < now() and o.effective_end_date > now()) and s.vgroup = 'FAMILY_TYPE_CODE'", nativeQuery = true)
        List<EmployeeBenefitContactRelships> findAllRelshipByEmployeeNumber(@Param("personNumber") String personNumber);

        // aman
        @Query(value = "select distinct(concat(person_id,contact_person_id)) from per_contact_relships_f", nativeQuery = true)
        public List<String> findAllByPersonIdAndContactPersonId();

        // aman
        @Query(value = "select * from per_contact_relships_f where person_id = :personId "
                        + " and contact_person_id = :contactPersonId ", nativeQuery = true)
        public Optional<PerContactRelshipsF> findByPersonIdAndContactPersonId(
                        @Param("personId") Long personId,
                        @Param("contactPersonId") Long contactPersonId);

        @Query(value = "SELECT CASE "
        		+ "        	WHEN EXISTS (SELECT 1  "
        		+ "        	        FROM per_contact_relships_f pcrf  "
        		+ "        	        WHERE pcrf.contact_type = 'S' AND pcrf.active = 0 and pcrf.person_id = :personId)  "
        		+ "        	     AND EXISTS (SELECT 1  "
        		+ "        			    FROM (  "
        		+ "        			      SELECT action  "
        		+ "        			      FROM approval_transaction at2  "
        		+ "        			      WHERE at2.modul = 'personInfo' AND at2.master_ref_id = :personId and JSON_UNQUOTE(JSON_EXTRACT(at2.new_data, '$.perContactRelshipsF.active'))  = 'false'  "
        		+ "        			      ORDER BY at2.id DESC  "
        		+ "        			      LIMIT 1  "
        		+ "        			    ) AS latest_action  "
        		+ "        			    WHERE latest_action.action NOT IN ('approve', 'reject'))  "
        		+ "        	    THEN   "
        		+ "        	    CASE         	       "
        		+ "        	    	 WHEN EXISTS (  "
        		+ "        			        SELECT 1  "
        		+ "        			     FROM per_contact_relships_f pcrf  "
        		+ "        			     WHERE pcrf.person_id = :personId  "
        		+ "        			       AND pcrf.active = 0 and pcrf.contact_type != 'S' )  "
        		+ "        			THEN 'Y'  "
        		+ "        	    	ELSE   "
        		+ "        	    	'N'  "
        		+ "        	    	end  "
        		+ "        	    else  "
        		+ "        		    CASE         	      "
        		+ "        	    	 WHEN EXISTS (  "
        		+ "        	    	     SELECT 1  "
        		+ "        	    	     FROM per_contact_relships_f pcrf  "
        		+ "        	    	     LEFT JOIN per_all_people_f papf ON papf.person_id = pcrf.contact_person_id AND papf.active = 0 and papf.person_id != :personId and papf.person_type != 'EMPLOYEE'  "
        		+ "        	    	     LEFT JOIN (select ppnf2.* from per_person_names_f ppnf2  "
        		+ "        	    		      join per_all_people_f papf2 on ppnf2.person_id=papf2.person_id and papf2.person_type != 'EMPLOYEE') ppnf   "
        		+ "        	    		      ON ppnf.person_id = pcrf.contact_person_id AND ppnf.active = 0 and ppnf.person_id != :personId  "
        		+ "        	    	     LEFT JOIN (select ppa2.* from per_person_addresses ppa2  "
        		+ "        	    		      join per_all_people_f papf2 on ppa2.person_id=papf2.person_id and papf2.person_type != 'EMPLOYEE') ppa   "
        		+ "        	    		      ON ppa.person_id = pcrf.contact_person_id AND ppa.active = 0 and ppa.person_id != :personId  "
        		+ "        	    	     LEFT JOIN (select pni2.* from per_national_identifiers pni2  "
        		+ "        	    		      join per_all_people_f papf2 on pni2.person_id=papf2.person_id and papf2.person_type != 'EMPLOYEE'   "
        		+ "        	    		      ) pni ON pni.person_id = pcrf.contact_person_id AND pni.active = 0 and pni.person_id != :personId  "
        		+ "        	    	     WHERE pcrf.person_id = :personId  "
        		+ "        	    	       AND (papf.person_id IS NOT NULL   "
        		+ "        	    	            OR ppnf.person_name_id IS NOT NULL   "
        		+ "        	    	            OR ppa.address_id IS NOT NULL   "
        		+ "        	    	            OR pni.national_identifier_id IS NOT NULL)  "
        		+ "        	    	 ) THEN 'Y'  "
        		+ "        	    	ELSE   "
        		+ "        	    	 CASE   "
        		+ "        		        WHEN EXISTS (  "
        		+ "        			        SELECT 1  "
        		+ "        			     FROM per_contact_relships_f pcrf  "
        		+ "        			     WHERE pcrf.person_id = :personId  "
        		+ "        			       AND pcrf.active = 0)   "
        		+ "        			    THEN 'Y'  "
        		+ "        		        ELSE  "
        		+ "	        		        CASE   "
        		+ "	        		        WHEN EXISTS (select 1  from approval_transaction at2  "
        		+ "								left join (select at3.approval_code from approval_transaction at3 where at3.`action` in ('approve','reject') group by at3.approval_code) at4 "
        		+ "								on at2.approval_code = at4.approval_code "
        		+ "								WHERE at4.approval_code IS null and at2.modul ='family' and at2.master_ref_id =:personId)   "
        		+ "	        			    THEN 'Y'  "
        		+ "	        		        ELSE 'N'  "
        		+ "	        		        end "
        		+ "        		      END  "
        		+ "        		  end  "
        		+ "        		 end as result", nativeQuery = true)
        public String checkProgressApproval( @Param("personId") Long personId);

        @Query(value = "SELECT CASE "
                + "    WHEN EXISTS ( "
                + "        SELECT 1 FROM per_contact_relships_f pcrf2 "
                + "        WHERE pcrf2.contact_relationship_id = :contactId "
                + "        AND pcrf2.active = 0 "
                + "    ) THEN 'Y' "
                + "    WHEN EXISTS ( "
                + "        SELECT 1 FROM approval_transaction at2 "
                + "        LEFT JOIN ( "
                + "            SELECT at3.approval_code FROM approval_transaction at3 "
                + "            WHERE at3.action IN ('approve','reject') "
                + "            GROUP BY at3.approval_code "
                + "        ) at4 ON at2.approval_code = at4.approval_code "
                + "        WHERE at4.approval_code IS NULL "
                + "        AND at2.modul = 'family' "
                + "        AND at2.master_ref_id = :personId "
                + "        AND at2.detail_ref_id = :contactId "
                + "    ) THEN 'Y' "
                + "    ELSE 'N' "
                + "END AS inProgressApproval", nativeQuery = true)
        String checkProgressApprovalByContactId(@Param("contactId") Long contactId, @Param("personId") Long personId);

        @Query(value = "select distinct(concat(contact_relationship_id,effective_start_date)) from per_contact_relships_f ", nativeQuery = true)
        List<String> findAllId();

        @Query(value = "select pcrf.contact_person_id as childsId, ppnf.last_name as childsName  from " +
                        "per_all_people_f papf " +
                        "INNER JOIN per_contact_relships_f pcrf on pcrf.person_id = papf.person_id and papf.person_number = :nip "
                        +
                        "INNER JOIN per_person_names_f ppnf on pcrf.contact_person_id = ppnf.person_id  and (CURDATE() >= ppnf.effective_start_date and CURDATE() <= ppnf.effective_end_date) "
                        +
                        "INNER JOIN system_config sc on sc.parameter = pcrf.contact_type " +
                        "where papf.person_number is not null " +
                        "AND (pcrf.effective_start_date <= CURDATE() AND pcrf.effective_end_date >= CURDATE()) " +
                        "AND sc.svalue like lower(CONCAT('%','child','%'))  " +
                        "AND (select COUNT(person_id) from per_all_people_f papf2 " +
                        "where papf2.date_of_death is null and pcrf.contact_person_id = papf2.person_id  and papf2.effective_start_date <= now() and papf2.effective_end_date >= now() )"
                        + " and papf.effective_start_date <= now() and papf.effective_end_date >= now()  ", nativeQuery = true)
        List<IEmployeeChildsIdAndName> getEligibleChildrenOfEmployee(@Param("nip") String nip);

    @Query(value = "SELECT papf2.person_id AS id, ppnf.last_name AS name, sc.svalue AS relationship, DATE_FORMAT(papf2.date_of_birth, '%d-%b-%Y') AS dateBirth, " +
            "CASE WHEN papf2.gender = 'M' THEN 'Male' WHEN papf2.gender = 'F' THEN 'Female' ELSE '' END AS gender " +
            "FROM per_all_people_f papf " +
            "INNER JOIN per_contact_relships_f pcrf ON pcrf.person_id = papf.person_id AND " +
            "CURDATE() BETWEEN pcrf.effective_start_date AND pcrf.effective_end_date " +
            "INNER JOIN per_person_names_f ppnf ON pcrf.contact_person_id = ppnf.person_id AND " +
            "CURDATE() BETWEEN ppnf.effective_start_date AND ppnf.effective_end_date " +
            "INNER JOIN system_config sc ON sc.parameter = pcrf.contact_type " +
            "INNER JOIN per_all_people_f papf2 ON papf2.person_id = pcrf.contact_person_id AND " +
            "CURDATE() BETWEEN papf2.effective_start_date AND papf2.effective_end_date " +
            "WHERE papf.person_number = :nip AND CURDATE() BETWEEN papf.effective_start_date AND papf.effective_end_date " +
            "AND (sc.svalue LIKE LOWER(CONCAT('%','child','%')) OR pcrf.contact_type = 'S' OR pcrf.contact_type = 'CIMB_S')",
        countQuery = "SELECT COUNT(1) " +
            "FROM per_all_people_f papf " +
            "INNER JOIN per_contact_relships_f pcrf ON pcrf.person_id = papf.person_id AND " +
            "CURDATE() BETWEEN pcrf.effective_start_date AND pcrf.effective_end_date " +
            "INNER JOIN per_person_names_f ppnf ON pcrf.contact_person_id = ppnf.person_id AND " +
            "CURDATE() BETWEEN ppnf.effective_start_date AND ppnf.effective_end_date " +
            "INNER JOIN system_config sc ON sc.parameter = pcrf.contact_type " +
            "INNER JOIN per_all_people_f papf2 ON papf2.person_id = pcrf.contact_person_id AND " +
            "CURDATE() BETWEEN papf2.effective_start_date AND papf2.effective_end_date " +
            "WHERE papf.person_number = :nip AND CURDATE() BETWEEN papf.effective_start_date AND papf.effective_end_date " +
            "AND (sc.svalue LIKE LOWER(CONCAT('%','child','%')) OR pcrf.contact_type = 'S' OR pcrf.contact_type = 'CIMB_S')",
        nativeQuery = true)
    Page<Map<String, Object>> getEligibleChildrenAndSpouseOfEmployee(@Param("nip") String nip, Pageable pageable);

    @Query(value = "SELECT papf2.person_id AS id, ppnf.last_name AS name, sc.svalue AS relationship, DATE_FORMAT(papf2.date_of_birth, '%d-%b-%Y') AS dateBirth, " +
        "CASE WHEN papf2.gender = 'M' THEN 'Male' WHEN papf2.gender = 'F' THEN 'Female' ELSE '' END AS gender " +
        "FROM per_all_people_f papf " +
        "INNER JOIN per_contact_relships_f pcrf ON pcrf.person_id = papf.person_id AND " +
        "CURDATE() BETWEEN pcrf.effective_start_date AND pcrf.effective_end_date " +
        "INNER JOIN per_person_names_f ppnf ON pcrf.contact_person_id = ppnf.person_id AND " +
        "CURDATE() BETWEEN ppnf.effective_start_date AND ppnf.effective_end_date " +
        "INNER JOIN system_config sc ON sc.parameter = pcrf.contact_type " +
        "INNER JOIN per_all_people_f papf2 ON papf2.person_id = pcrf.contact_person_id AND " +
        "CURDATE() BETWEEN papf2.effective_start_date AND papf2.effective_end_date " +
        "WHERE papf.person_number = :nip AND CURDATE() BETWEEN papf.effective_start_date AND papf.effective_end_date " +
        "AND (sc.svalue LIKE LOWER(CONCAT('%','child','%')) OR pcrf.contact_type = 'S' OR pcrf.contact_type = 'CIMB_S')",
        nativeQuery = true)
    List<Map<String, Object>> getEligibleChildrenAndSpouseOfEmployeeList(@Param("nip") String nip);

        @Query(value = "select n.last_name AS familyName "
        		+ ", m1.gender AS gender "
        		+ ", m1.livingstatus AS livingStatus "
        		+ ", c.contact_type AS relationshipDetail "
        		+ ", c.contact_relationship_id AS fsContactId "
        		+ ", c.effective_start_date AS startDateContact "
        		+ ", c.emergency_contact_flag AS emergencyContactFlag "
        		+ ", DATE_FORMAT(m1.date_of_death, '%d %b %Y') as dateOfDeath "
        		+ ", phones.contactNumber  "
        		+ "FROM per_contact_relships_f c "
        		+ "join per_all_people_f m on c.person_id =m.person_id and curdate() between m.effective_start_date and m.effective_end_date  "
        		+ "JOIN per_all_people_f m1 ON c.contact_person_id = m1.person_id  and curdate() between m1.effective_start_date and m1.effective_end_date   "
        		+ "left JOIN per_person_names_f  n ON n.person_id = c.contact_person_id and curdate() between n.effective_start_date and n.effective_end_date   "
        		+ "LEFT JOIN (SELECT pp.person_id, GROUP_CONCAT(CONCAT(IFNULL(pp.area_code, ''), pp.phone_number) SEPARATOR ', ') AS contactNumber "
        		+ " FROM per_phones pp WHERE pp.phone_type IN ('HM', 'M') AND (curdate()  between  pp.date_from AND pp.date_to)  "
        		+ " GROUP BY pp.person_id ) AS phones ON c.contact_person_id = phones.person_id "
        		+ "where m.person_number =:nip and curdate() between c.effective_start_date and c.effective_end_date", nativeQuery = true)
        List<FamilyDTO> getFamilyEmployee(@Param("nip") String nip);

        @Query(value = "select * from per_contact_relships_f pcrf where pcrf.person_id =:personId and pcrf.contact_person_id =:contactPersonId "
        		+ "and curdate() between  pcrf.effective_start_date and pcrf.effective_end_date limit 1", nativeQuery = true)
        public Optional<PerContactRelshipsF> findFamilyByContactPersonIdAndPersonId(@Param("personId") Long personId,@Param("contactPersonId") Long contactPersonId);

        public List<PerContactRelshipsF> findByContactTypeAndPersonIdAndActive(String contactType, Long personId, boolean active);

        @Query(value = "SELECT papf.person_number  from per_contact_relships_f pcrf  "
        		+ "join per_all_people_f papf on pcrf.person_id = papf.person_id and :filterDate BETWEEN papf.effective_start_date and papf.effective_end_date  "
        		+ "where pcrf.contact_person_id = :contactPersonId order by pcrf.effective_end_date desc limit 1", nativeQuery = true)
        Optional<String> findNipEmployee( @Param("contactPersonId") Long contactPersonId, @Param("filterDate") LocalDate filterDate);


    @Query(value = "SELECT papf2.person_id AS id, ppnf.last_name AS name, sc.svalue AS relationship, DATE_FORMAT(papf2.date_of_birth, '%d-%b-%Y') AS dateBirth, " +
        "CASE WHEN papf2.gender = 'M' THEN 'Male' WHEN papf2.gender = 'F' THEN 'Female' ELSE '' END AS gender " +
        "FROM per_all_people_f papf " +
        "INNER JOIN per_contact_relships_f pcrf ON pcrf.person_id = papf.person_id AND " +
        "CURDATE() BETWEEN pcrf.effective_start_date AND pcrf.effective_end_date " +
        "INNER JOIN per_person_names_f ppnf ON pcrf.contact_person_id = ppnf.person_id AND " +
        "CURDATE() BETWEEN ppnf.effective_start_date AND ppnf.effective_end_date " +
        "INNER JOIN system_config sc ON sc.parameter = pcrf.contact_type " +
        "INNER JOIN per_all_people_f papf2 ON papf2.person_id = pcrf.contact_person_id AND " +
        "CURDATE() BETWEEN papf2.effective_start_date AND papf2.effective_end_date " +
        "WHERE papf.person_number = :nip AND CURDATE() BETWEEN papf.effective_start_date AND papf.effective_end_date " +
        "AND (sc.svalue LIKE LOWER(CONCAT('%','child','%')) OR pcrf.contact_type is not null)",
        countQuery = "SELECT COUNT(1) " +
            "FROM per_all_people_f papf " +
            "INNER JOIN per_contact_relships_f pcrf ON pcrf.person_id = papf.person_id AND " +
            "CURDATE() BETWEEN pcrf.effective_start_date AND pcrf.effective_end_date " +
            "INNER JOIN per_person_names_f ppnf ON pcrf.contact_person_id = ppnf.person_id AND " +
            "CURDATE() BETWEEN ppnf.effective_start_date AND ppnf.effective_end_date " +
            "INNER JOIN system_config sc ON sc.parameter = pcrf.contact_type " +
            "INNER JOIN per_all_people_f papf2 ON papf2.person_id = pcrf.contact_person_id AND " +
            "CURDATE() BETWEEN papf2.effective_start_date AND papf2.effective_end_date " +
            "WHERE papf.person_number = :nip AND CURDATE() BETWEEN papf.effective_start_date AND papf.effective_end_date " +
            "AND (sc.svalue LIKE LOWER(CONCAT('%','child','%')) OR pcrf.contact_type is not null)",
        nativeQuery = true)
    Page<Map<String, Object>> getEligibleFaimyEmployee(@Param("nip") String nip, Pageable pageable);

}
